using Autofac;
using Autofac.Extras.DynamicProxy;
using Business.Abstract;
using Business.Concrete;
using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using Core.Utilities.Security.JWT;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.DependencyResolvers.Autofac
{
    public class AutofacBusinessModule:Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterType<UserManager>().As<IUserService>().SingleInstance();
            builder.RegisterType<EfUserDal>().As<IUserDal>().SingleInstance();
            builder.RegisterType<CompanyManager>().As<ICompanyService>().SingleInstance();
            builder.RegisterType<EfCompanyDal>().As<ICompanyDal>().SingleInstance();
            builder.RegisterType<CompanyAdressManager>().As<ICompanyAdressService>().SingleInstance();
            builder.RegisterType<EfCompanyAdressDal>().As<ICompanyAdressDal>().SingleInstance();
            builder.RegisterType<MemberManager>().As<IMemberService>().SingleInstance();
            builder.RegisterType<EfMemberDal>().As<IMemberDal>().SingleInstance();
            builder.RegisterType<MembershipTypeManager>().As<IMembershipTypeService>().SingleInstance();
            builder.RegisterType<EfMembershipTypeDal>().As<IMembershiptypeDal>().SingleInstance();
            builder.RegisterType<MembershipManager>().As<IMembershipService>().SingleInstance();
            builder.RegisterType<EfMembershipDal>().As<IMembershipDal>().SingleInstance();
            builder.RegisterType<CompanyUserManager>().As<ICompanyUserService>().SingleInstance();
            builder.RegisterType<EfCompanyUserDal>().As<ICompanyUserDal>().SingleInstance();
            builder.RegisterType<PaymentManager>().As<IPaymentService>().SingleInstance();
            builder.RegisterType<EfPaymentDal>().As<IPaymentDal>().SingleInstance();
            builder.RegisterType<UserCompanyManager>().As<IUserCompanyService>().SingleInstance();
            builder.RegisterType<EfUserCompanyDal>().As<IUserCompanyDal>().SingleInstance();
            builder.RegisterType<UnifiedCompanyManager>().As<IUnifiedCompanyService>().SingleInstance();
            builder.RegisterType<EntryExitHistoryManager>().As<IEntryExitHistoryService>().SingleInstance();
            builder.RegisterType<EfEntryExitHistoryDal>().As<IEntryExitHistoryDal>().SingleInstance();
            builder.RegisterType<CityManager>().As<ICityService>().SingleInstance();
            builder.RegisterType<EfCityDal>().As<ICityDal>().SingleInstance();
            builder.RegisterType<TownManager>().As<ITownService>().SingleInstance();
            builder.RegisterType<EfTownDal>().As<ITownDal>().SingleInstance();
            builder.RegisterType<AuthManager>().As<IAuthService>();
            builder.RegisterType<JwtHelper>().As<ITokenHelper>();
            builder.RegisterType<ProductManager>().As<IProductService>().SingleInstance();
            builder.RegisterType<EfProductDal>().As<IProductDal>().SingleInstance();
            builder.RegisterType<TransactionManager>().As<ITransactionService>().SingleInstance();
            builder.RegisterType<EfTransactionDal>().As<ITransactionDal>().SingleInstance();
            builder.RegisterType<OperationClaimManager>().As<IOperationClaimService>().SingleInstance();
            builder.RegisterType<EfOperationClaimDal>().As<IOperationClaimDal>().SingleInstance();
            builder.RegisterType<UserOperationClaimManager>().As<IUserOperationClaimService>().SingleInstance();
            builder.RegisterType<EfUserOperationClaimDal>().As<IUserOperationClaimDal>().SingleInstance();
            builder.RegisterType<RemainingDebtManager>().As<IRemainingDebtService>().SingleInstance();
            builder.RegisterType<EfRemainingDebtDal>().As<IRemainingDebtDal>().SingleInstance();
            builder.RegisterType<EfDebtPaymentDal>().As<IDebtPaymentDal>().SingleInstance();
            builder.RegisterType<DebtPaymentManager>().As<IDebtPaymentService>().SingleInstance();
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().SingleInstance();

            // Multi-tenant Company Context
            builder.RegisterType<CompanyContext>().As<ICompanyContext>().InstancePerLifetimeScope();

            builder.RegisterType<UserDeviceManager>().As<IUserDeviceService>().SingleInstance();
            builder.RegisterType<EfUserDeviceDal>().As<IUserDeviceDal>().SingleInstance();
            builder.RegisterType<MembershipFreezeHistoryManager>().As<IMembershipFreezeHistoryService>().SingleInstance();
            builder.RegisterType<EfMembershipFreezeHistoryDal>().As<IMembershipFreezeHistoryDal>().SingleInstance();
            builder.RegisterType<LicensePackageManager>().As<ILicensePackageService>().SingleInstance();
            builder.RegisterType<EfLicensePackageDal>().As<ILicensePackageDal>().SingleInstance();
            builder.RegisterType<UserLicenseManager>().As<IUserLicenseService>().SingleInstance();
            builder.RegisterType<EfUserLicenseDal>().As<IUserLicenseDal>().SingleInstance();
            builder.RegisterType<LicenseTransactionManager>().As<ILicenseTransactionService>().SingleInstance();
            builder.RegisterType<EfLicenseTransactionDal>().As<ILicenseTransactionDal>().SingleInstance();

            // Expense Service ve Dal Kayıtları
            builder.RegisterType<ExpenseManager>().As<IExpenseService>().SingleInstance();
            builder.RegisterType<EfExpenseDal>().As<IExpenseDal>().SingleInstance();

            // File Service Kaydı
            builder.RegisterType<FileManager>().As<IFileService>().SingleInstance();

            // Profile Service Kaydı
            builder.RegisterType<ProfileManager>().As<IProfileService>().SingleInstance();

            // Advanced Rate Limit Service Kaydı
            builder.RegisterType<AdvancedRateLimitManager>().As<IAdvancedRateLimitService>().SingleInstance();

            // QR Code Encryption Service Kaydı
            builder.RegisterType<QrCodeEncryptionManager>().As<IQrCodeEncryptionService>().SingleInstance();

            // Cache Service Kaydı
            builder.RegisterType<CacheManager>().As<ICacheService>().SingleInstance();

            // Egzersiz Sistemi Service ve Dal Kayıtları
            builder.RegisterType<ExerciseCategoryManager>().As<IExerciseCategoryService>().SingleInstance();
            builder.RegisterType<EfExerciseCategoryDal>().As<IExerciseCategoryDal>().SingleInstance();
            builder.RegisterType<SystemExerciseManager>().As<ISystemExerciseService>().SingleInstance();
            builder.RegisterType<EfSystemExerciseDal>().As<ISystemExerciseDal>().SingleInstance();
            builder.RegisterType<CompanyExerciseManager>().As<ICompanyExerciseService>().SingleInstance();
            builder.RegisterType<EfCompanyExerciseDal>().As<ICompanyExerciseDal>().SingleInstance();

            // Antrenman Programı Sistemi
            builder.RegisterType<WorkoutProgramTemplateManager>().As<IWorkoutProgramTemplateService>().SingleInstance();
            builder.RegisterType<EfWorkoutProgramTemplateDal>().As<IWorkoutProgramTemplateDal>().SingleInstance();
            builder.RegisterType<EfWorkoutProgramDayDal>().As<IWorkoutProgramDayDal>().SingleInstance();
            builder.RegisterType<EfWorkoutProgramExerciseDal>().As<IWorkoutProgramExerciseDal>().SingleInstance();

            // Üye Program Atama Sistemi
            builder.RegisterType<MemberWorkoutProgramManager>().As<IMemberWorkoutProgramService>().SingleInstance();
            builder.RegisterType<EfMemberWorkoutProgramDal>().As<IMemberWorkoutProgramDal>().SingleInstance();

            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(assembly).AsImplementedInterfaces()
                .EnableInterfaceInterceptors(new ProxyGenerationOptions()
                {
                    Selector = new AspectInterceptorSelector()
                }).SingleInstance();
        }
    }
}

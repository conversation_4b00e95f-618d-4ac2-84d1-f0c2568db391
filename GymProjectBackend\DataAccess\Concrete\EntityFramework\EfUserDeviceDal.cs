using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDeviceDal : EfEntityRepositoryBase<UserDevice, GymContext>, IUserDeviceDal
    {
        public EfUserDeviceDal(GymContext context) : base(context)
        {
        }

        public List<UserDevice> GetActiveDevicesByUserId(int userId)
        {
            return _context.UserDevices
                .Where(d => d.UserId == userId && d.IsActive)
                .ToList();
        }

        public UserDevice GetByRefreshToken(string refreshToken)
        {
            return _context.UserDevices
                .FirstOrDefault(d => d.RefreshToken == refreshToken && d.IsActive);
        }
    }
}
